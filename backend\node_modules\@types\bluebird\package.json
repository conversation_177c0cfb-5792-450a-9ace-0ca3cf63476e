{"name": "@types/bluebird", "version": "3.5.42", "description": "TypeScript definitions for bluebird", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/bluebird", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/lhecker"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/bluebird"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "00aa07922f070eaa03e5e9566412433716ee34f248972d8d2e8a6f55410c2f3f", "typeScriptVersion": "4.5"}