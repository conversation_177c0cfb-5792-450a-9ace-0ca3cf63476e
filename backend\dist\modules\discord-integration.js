"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.discordIntegration = void 0;
const discord_js_1 = require("discord.js");
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
class DiscordIntegration {
    constructor() {
        this.client = null;
    }
    async initialize() {
        this.client = new discord_js_1.Client({ intents: ['Guilds', 'GuildMessages'] });
        this.client.on('ready', () => {
            console.log('Discord bot is ready!');
        });
        await this.client.login(process.env.DISCORD_BOT_TOKEN);
    }
    async sendMessage(channelId, message) {
        if (!this.client) {
            throw new Error('Discord bot not initialized');
        }
        const channel = await this.client.channels.fetch(channelId);
        if (!channel) {
            throw new Error('Channel not found');
        }
        await channel.send(message);
    }
}
exports.discordIntegration = new DiscordIntegration();
