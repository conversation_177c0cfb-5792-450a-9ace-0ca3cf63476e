{"name": "@types/continuation-local-storage", "version": "3.2.7", "description": "TypeScript definitions for continuation-local-storage", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/continuation-local-storage", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "rath", "url": "https://github.com/rath"}, {"name": "<PERSON><PERSON>", "githubUsername": "heycalmdown", "url": "https://github.com/heycalmdown"}, {"name": "<PERSON>", "githubUsername": "skwee357", "url": "https://github.com/skwee357"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/continuation-local-storage"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "aa5433c70e474f9327cf93371c112bb489e09ed3e923a7b2dcb918de8f272e0a", "typeScriptVersion": "4.5"}