"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const rest_1 = require("@discordjs/rest");
const v9_1 = require("discord-api-types/v9");
const dotenv_1 = __importDefault(require("dotenv"));
const watch_add_1 = require("./commands/watch-add");
const watch_remove_1 = require("./commands/watch-remove");
const watch_list_1 = require("./commands/watch-list");
dotenv_1.default.config();
const client = new discord_js_1.Client({ intents: [discord_js_1.GatewayIntentBits.Guilds, discord_js_1.GatewayIntentBits.GuildMessages] });
const commands = [
    watch_add_1.watchAdd.data.toJSON(),
    watch_remove_1.watchRemove.data.toJSON(),
    watch_list_1.watchList.data.toJSON(),
];
const rest = new rest_1.REST({ version: '9' }).setToken(process.env.DISCORD_BOT_TOKEN || '');
(async () => {
    try {
        console.log('Started refreshing application (/) commands.');
        await rest.put(v9_1.Routes.applicationCommands(process.env.DISCORD_CLIENT_ID || ''), { body: commands });
        console.log('Successfully reloaded application (/) commands.');
    }
    catch (error) {
        console.error(error);
    }
})();
client.on('interactionCreate', async (interaction) => {
    if (!interaction.isChatInputCommand())
        return;
    if (interaction.commandName === watch_add_1.watchAdd.data.name) {
        await watch_add_1.watchAdd.execute(interaction);
    }
    else if (interaction.commandName === watch_remove_1.watchRemove.data.name) {
        await watch_remove_1.watchRemove.execute(interaction);
    }
    else if (interaction.commandName === watch_list_1.watchList.data.name) {
        await watch_list_1.watchList.execute(interaction);
    }
});
client.on('ready', () => {
    console.log('Discord bot is ready!');
});
client.login(process.env.DISCORD_BOT_TOKEN);
