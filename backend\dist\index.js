"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const browser_manager_1 = require("./modules/browser-manager");
const discord_integration_js_1 = require("./modules/discord-integration.js");
const db_1 = require("./db");
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
const app = (0, express_1.default)();
const port = process.env.PORT || 3000;
app.use(express_1.default.json());
async function start() {
    try {
        await db_1.sequelize.sync();
        console.log('Database connected');
        await browser_manager_1.browserManager.initialize();
        console.log('Browser manager initialized');
        await discord_integration_js_1.discordIntegration.initialize();
        console.log('Discord integration initialized');
        app.listen(port, () => {
            console.log(`Server is running on port ${port}`);
        });
    }
    catch (error) {
        console.error('Error starting the application:', error);
    }
}
start();
