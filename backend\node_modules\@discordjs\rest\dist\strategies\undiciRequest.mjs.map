{"version": 3, "sources": ["../../src/strategies/undiciRequest.ts"], "sourcesContent": ["import { STATUS_CODES } from 'node:http';\nimport { URLSearchParams } from 'node:url';\nimport { types } from 'node:util';\nimport { type RequestInit, request, Headers } from 'undici';\nimport type { ResponseLike } from '../shared.js';\n\nexport type RequestOptions = Exclude<Parameters<typeof request>[1], undefined>;\n\nexport async function makeRequest(url: string, init: RequestInit): Promise<ResponseLike> {\n\t// The cast is necessary because `headers` and `method` are narrower types in `undici.request`\n\t// our request path guarantees they are of acceptable type for `undici.request`\n\tconst options = {\n\t\t...init,\n\t\tbody: await resolveBody(init.body),\n\t} as RequestOptions;\n\tconst res = await request(url, options);\n\treturn {\n\t\tbody: res.body,\n\t\tasync arrayBuffer() {\n\t\t\treturn res.body.arrayBuffer();\n\t\t},\n\t\tasync json() {\n\t\t\treturn res.body.json();\n\t\t},\n\t\tasync text() {\n\t\t\treturn res.body.text();\n\t\t},\n\t\tget bodyUsed() {\n\t\t\treturn res.body.bodyUsed;\n\t\t},\n\t\theaders: new Headers(res.headers as Record<string, string[] | string>),\n\t\tstatus: res.statusCode,\n\t\tstatusText: STATUS_CODES[res.statusCode]!,\n\t\tok: res.statusCode >= 200 && res.statusCode < 300,\n\t};\n}\n\nexport async function resolveBody(body: RequestInit['body']): Promise<Exclude<RequestOptions['body'], undefined>> {\n\t// eslint-disable-next-line no-eq-null, eqeqeq\n\tif (body == null) {\n\t\treturn null;\n\t} else if (typeof body === 'string') {\n\t\treturn body;\n\t} else if (types.isUint8Array(body)) {\n\t\treturn body;\n\t} else if (types.isArrayBuffer(body)) {\n\t\treturn new Uint8Array(body);\n\t} else if (body instanceof URLSearchParams) {\n\t\treturn body.toString();\n\t} else if (body instanceof DataView) {\n\t\treturn new Uint8Array(body.buffer);\n\t} else if (body instanceof Blob) {\n\t\treturn new Uint8Array(await body.arrayBuffer());\n\t} else if (body instanceof FormData) {\n\t\treturn body;\n\t} else if ((body as Iterable<Uint8Array>)[Symbol.iterator]) {\n\t\tconst chunks = [...(body as Iterable<Uint8Array>)];\n\n\t\treturn Buffer.concat(chunks);\n\t} else if ((body as AsyncIterable<Uint8Array>)[Symbol.asyncIterator]) {\n\t\tconst chunks: Uint8Array[] = [];\n\n\t\tfor await (const chunk of body as AsyncIterable<Uint8Array>) {\n\t\t\tchunks.push(chunk);\n\t\t}\n\n\t\treturn Buffer.concat(chunks);\n\t}\n\n\tthrow new TypeError(`Unable to resolve body.`);\n}\n"], "mappings": ";;;;AAAA,SAAS,oBAAoB;AAC7B,SAAS,uBAAuB;AAChC,SAAS,aAAa;AACtB,SAA2B,SAAS,eAAe;AAKnD,eAAsB,YAAY,KAAa,MAA0C;AAGxF,QAAM,UAAU;AAAA,IACf,GAAG;AAAA,IACH,MAAM,MAAM,YAAY,KAAK,IAAI;AAAA,EAClC;AACA,QAAM,MAAM,MAAM,QAAQ,KAAK,OAAO;AACtC,SAAO;AAAA,IACN,MAAM,IAAI;AAAA,IACV,MAAM,cAAc;AACnB,aAAO,IAAI,KAAK,YAAY;AAAA,IAC7B;AAAA,IACA,MAAM,OAAO;AACZ,aAAO,IAAI,KAAK,KAAK;AAAA,IACtB;AAAA,IACA,MAAM,OAAO;AACZ,aAAO,IAAI,KAAK,KAAK;AAAA,IACtB;AAAA,IACA,IAAI,WAAW;AACd,aAAO,IAAI,KAAK;AAAA,IACjB;AAAA,IACA,SAAS,IAAI,QAAQ,IAAI,OAA4C;AAAA,IACrE,QAAQ,IAAI;AAAA,IACZ,YAAY,aAAa,IAAI,UAAU;AAAA,IACvC,IAAI,IAAI,cAAc,OAAO,IAAI,aAAa;AAAA,EAC/C;AACD;AA3BsB;AA6BtB,eAAsB,YAAY,MAAgF;AAEjH,MAAI,QAAQ,MAAM;AACjB,WAAO;AAAA,EACR,WAAW,OAAO,SAAS,UAAU;AACpC,WAAO;AAAA,EACR,WAAW,MAAM,aAAa,IAAI,GAAG;AACpC,WAAO;AAAA,EACR,WAAW,MAAM,cAAc,IAAI,GAAG;AACrC,WAAO,IAAI,WAAW,IAAI;AAAA,EAC3B,WAAW,gBAAgB,iBAAiB;AAC3C,WAAO,KAAK,SAAS;AAAA,EACtB,WAAW,gBAAgB,UAAU;AACpC,WAAO,IAAI,WAAW,KAAK,MAAM;AAAA,EAClC,WAAW,gBAAgB,MAAM;AAChC,WAAO,IAAI,WAAW,MAAM,KAAK,YAAY,CAAC;AAAA,EAC/C,WAAW,gBAAgB,UAAU;AACpC,WAAO;AAAA,EACR,WAAY,KAA8B,OAAO,QAAQ,GAAG;AAC3D,UAAM,SAAS,CAAC,GAAI,IAA6B;AAEjD,WAAO,OAAO,OAAO,MAAM;AAAA,EAC5B,WAAY,KAAmC,OAAO,aAAa,GAAG;AACrE,UAAM,SAAuB,CAAC;AAE9B,qBAAiB,SAAS,MAAmC;AAC5D,aAAO,KAAK,KAAK;AAAA,IAClB;AAEA,WAAO,OAAO,OAAO,MAAM;AAAA,EAC5B;AAEA,QAAM,IAAI,UAAU,yBAAyB;AAC9C;AAjCsB;", "names": []}