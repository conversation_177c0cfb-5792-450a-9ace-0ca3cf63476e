import { Client, GatewayIntentBits, SlashCommandBuilder } from 'discord.js';
import { REST } from '@discordjs/rest';
import { Routes } from 'discord-api-types/v9';
import dotenv from 'dotenv';
import { watchAdd } from './commands/watch-add';
import { watchRemove } from './commands/watch-remove';
import { watchList } from './commands/watch-list';

dotenv.config();

const client = new Client({ intents: [GatewayIntentBits.Guilds, GatewayIntentBits.GuildMessages] });

const commands = [
  watchAdd.data.toJSON(),
  watchRemove.data.toJSON(),
  watchList.data.toJSON(),
];

const rest = new REST({ version: '9' }).setToken(process.env.DISCORD_BOT_TOKEN || '');

(async () => {
  try {
    console.log('Started refreshing application (/) commands.');

    await rest.put(
      Routes.applicationCommands(process.env.DISCORD_CLIENT_ID || ''),
      { body: commands },
    );

    console.log('Successfully reloaded application (/) commands.');
  } catch (error) {
    console.error(error);
  }
})();

client.on('interactionCreate', async interaction => {
  if (!interaction.isChatInputCommand()) return;

  if (interaction.commandName === watchAdd.data.name) {
    await watchAdd.execute(interaction);
  } else if (interaction.commandName === watchRemove.data.name) {
    await watchRemove.execute(interaction);
  } else if (interaction.commandName === watchList.data.name) {
    await watchList.execute(interaction);
  }
});

client.on('ready', () => {
  console.log('Discord bot is ready!');
});

client.login(process.env.DISCORD_BOT_TOKEN);
