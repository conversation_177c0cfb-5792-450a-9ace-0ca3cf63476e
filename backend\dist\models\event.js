"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Event = void 0;
const sequelize_1 = require("sequelize");
const db_1 = require("../db");
class Event extends sequelize_1.Model {
}
exports.Event = Event;
Event.init({
    xUsername: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
    },
    eventType: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
    },
    eventContent: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
    },
    timestamp: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
    },
}, {
    sequelize: db_1.sequelize,
    modelName: 'Event',
});
