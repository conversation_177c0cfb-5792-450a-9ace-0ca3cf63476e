{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../ts/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBA,iBAAiB;AACjB,yCASmB;AAEjB,qGAVA,+BAAoB,OAUA;AACpB,0GAVA,oCAAyB,OAUA;AACzB,4FAVA,sBAAW,OAUA;AACX,2FAVA,qBAAU,OAUA;AACV,8FAVA,wBAAa,OAUA;AACb,6FAVA,uBAAY,OAUA;AAKd,iBAAiB;AACjB,iDAA8B;AAC9B,6CAA0B;AAC1B,8DAA8D;AAC9D,mDAAkC;AAClC,qDAAkC;AAClC,gDAA6B;AA0B7B,IAAI,SAAS,GAAkC,SAAS,CAAA;AACxD,IAAI,gBAAgB,GAA2C,SAAS,CAAA;AAExE;;;;;;;;;;;;;;GAcG;AACI,KAAK,UAAU,UAAU;IAC9B,gBAAgB,KAAhB,gBAAgB,GAAK,IAAA,+BAAoB,GAAE,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;QAC5D,SAAS,GAAG,QAAQ,CAAA;QACpB,OAAO,QAAQ,CAAA;IACjB,CAAC,CAAC,EAAA;IACF,OAAO,MAAM,gBAAgB,CAAA;AAC/B,CAAC;AAND,gCAMC;AAED;;;;GAIG;AACH,SAAgB,cAAc;IAC5B,IAAI,CAAC,SAAS,EAAE;QACd,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAA;KAC9E;IACD,OAAO,SAAS,CAAA;AAClB,CAAC;AALD,wCAKC;AAED;;;;;;;;;;GAUG;AACI,KAAK,UAAU,eAAe,CAAC,OAA6B;IACjE,MAAM,MAAM,GAAG,MAAM,IAAA,oCAAyB,GAAE,CAAA;IAChD,OAAO,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;AACnC,CAAC;AAHD,0CAGC;AAED;;;;;;;;;;;GAWG;AACI,KAAK,UAAU,eAAe,CAAC,OAAwB;IAC5D,MAAM,MAAM,GAAG,MAAM,IAAA,oCAAyB,GAAE,CAAA;IAChD,OAAO,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;AACnC,CAAC;AAHD,0CAGC;AAED;;;;;GAKG;AACH,SAAgB,4BAA4B,CAAC,QAAuB;IAClE,MAAM,gBAAgB,GAAG,OAAO,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAA;IAErF,OAAO;QACL,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,gBAAgB,CAAA;IACtC,CAAC,CAAA;AACH,CAAC;AAND,oEAMC", "sourcesContent": ["// Sync classes\nimport type { QuickJ<PERSON>WASMModule } from \"./module\"\nimport type { <PERSON><PERSON><PERSON>unt<PERSON>, InterruptHandler } from \"./runtime\"\nimport type { QuickJSContext } from \"./context\"\nexport type { QuickJSWASMModule, QuickJSContext, QuickJSRuntime }\n\n// Async classes\nimport type { QuickJSAsyncWASMModule } from \"./module-asyncify\"\nimport type { QuickJSAsyncRuntime } from \"./runtime-asyncify\"\nimport type { QuickJSAsyncContext, AsyncFunctionImplementation } from \"./context-asyncify\"\nimport { AsyncRuntimeOptions, ContextOptions } from \"./types\"\nexport type {\n  QuickJSAsyncContext,\n  QuickJSAsyncRuntime,\n  QuickJSAsyncWASMModule,\n  AsyncFunctionImplementation,\n}\n\n// Build variants\nimport {\n  newQuickJSWASMModule,\n  newQuickJSAsyncWASMModule,\n  DEBUG_ASYNC,\n  DEBUG_SYNC,\n  RELEASE_ASYNC,\n  RELEASE_SYNC,\n  Sync<PERSON>uildVariant,\n  AsyncBuildVariant,\n} from \"./variants\"\nexport {\n  newQuickJSWASMModule,\n  newQuickJSAsyncWASMModule,\n  DEBUG_ASYNC,\n  DEBUG_SYNC,\n  RELEASE_ASYNC,\n  RELEASE_SYNC,\n  SyncBuildVariant,\n  AsyncBuildVariant,\n}\n\n// Export helpers\nexport * from \"./vm-interface\"\nexport * from \"./lifetime\"\n/** Collects the informative errors this library may throw. */\nexport * as errors from \"./errors\"\nexport * from \"./deferred-promise\"\nexport * from \"./module-test\"\nexport type {\n  StaticJSValue,\n  JSValueConst,\n  JSValue,\n  QuickJSHandle,\n  ContextOptions,\n  ContextEvalOptions,\n  RuntimeOptions,\n  AsyncRuntimeOptions,\n  RuntimeOptionsBase,\n  JSModuleLoader,\n  JSModuleLoadResult,\n  JSModuleLoaderAsync,\n  JSModuleLoadSuccess,\n  JSModuleLoadFailure,\n  JSModuleNormalizer,\n  JSModuleNormalizerAsync,\n  JSModuleNormalizeResult,\n  JSModuleNormalizeFailure,\n  JSModuleNormalizeSuccess,\n} from \"./types\"\nexport type { ModuleEvalOptions } from \"./module\"\nexport type { InterruptHandler, ExecutePendingJobsResult } from \"./runtime\"\nexport type { QuickJSPropertyKey } from \"./context\"\n\nlet singleton: QuickJSWASMModule | undefined = undefined\nlet singletonPromise: Promise<QuickJSWASMModule> | undefined = undefined\n\n/**\n * Get a shared singleton {@link QuickJSWASMModule}. Use this to evaluate code\n * or create Javascript environments.\n *\n * This is the top-level entrypoint for the quickjs-emscripten library.\n *\n * If you need strictest possible isolation guarantees, you may create a\n * separate {@link QuickJSWASMModule} via {@link newQuickJSWASMModule}.\n *\n * To work with the asyncified version of this library, see these functions:\n *\n * - {@link newAsyncRuntime}.\n * - {@link newAsyncContext}.\n * - {@link newQuickJSAsyncWASMModule}.\n */\nexport async function getQuickJS(): Promise<QuickJSWASMModule> {\n  singletonPromise ??= newQuickJSWASMModule().then((instance) => {\n    singleton = instance\n    return instance\n  })\n  return await singletonPromise\n}\n\n/**\n * Provides synchronous access to the shared {@link QuickJSWASMModule} instance returned by {@link getQuickJS}, as long as\n * least once.\n * @throws If called before `getQuickJS` resolves.\n */\nexport function getQuickJSSync(): QuickJSWASMModule {\n  if (!singleton) {\n    throw new Error(\"QuickJS not initialized. Await getQuickJS() at least once.\")\n  }\n  return singleton\n}\n\n/**\n * Create a new [[QuickJSAsyncRuntime]] in a separate WebAssembly module.\n *\n * Each runtime is isolated in a separate WebAssembly module, so that errors in\n * one runtime cannot contaminate another runtime, and each runtime can execute\n * an asynchronous action without conflicts.\n *\n * Note that there is a hard limit on the number of WebAssembly modules in older\n * versions of v8:\n * https://bugs.chromium.org/p/v8/issues/detail?id=12076\n */\nexport async function newAsyncRuntime(options?: AsyncRuntimeOptions): Promise<QuickJSAsyncRuntime> {\n  const module = await newQuickJSAsyncWASMModule()\n  return module.newRuntime(options)\n}\n\n/**\n * Create a new [[QuickJSAsyncContext]] (with an associated runtime) in an\n * separate WebAssembly module.\n *\n * Each context is isolated in a separate WebAssembly module, so that errors in\n * one runtime cannot contaminate another runtime, and each runtime can execute\n * an asynchronous action without conflicts.\n *\n * Note that there is a hard limit on the number of WebAssembly modules in older\n * versions of v8:\n * https://bugs.chromium.org/p/v8/issues/detail?id=12076\n */\nexport async function newAsyncContext(options?: ContextOptions): Promise<QuickJSAsyncContext> {\n  const module = await newQuickJSAsyncWASMModule()\n  return module.newContext(options)\n}\n\n/**\n * Returns an interrupt handler that interrupts Javascript execution after a deadline time.\n *\n * @param deadline - Interrupt execution if it's still running after this time.\n *   Number values are compared against `Date.now()`\n */\nexport function shouldInterruptAfterDeadline(deadline: Date | number): InterruptHandler {\n  const deadlineAsNumber = typeof deadline === \"number\" ? deadline : deadline.getTime()\n\n  return function () {\n    return Date.now() > deadlineAsNumber\n  }\n}\n"]}