"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Subscription = void 0;
const sequelize_1 = require("sequelize");
const db_1 = require("../db");
class Subscription extends sequelize_1.Model {
}
exports.Subscription = Subscription;
Subscription.init({
    channelId: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
    },
    xUsername: {
        type: sequelize_1.DataTypes.STRING,
        allowNull: false,
    },
    eventTypes: {
        type: sequelize_1.DataTypes.ARRAY(sequelize_1.DataTypes.STRING),
        allowNull: false,
    },
}, {
    sequelize: db_1.sequelize,
    modelName: 'Subscription',
});
