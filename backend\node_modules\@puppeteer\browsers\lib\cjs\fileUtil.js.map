{"version": 3, "file": "fileUtil.js", "sourceRoot": "", "sources": ["../../src/fileUtil.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,iDAAuD;AACvD,2BAAoC;AACpC,0CAA2C;AAC3C,2CAA6B;AAC7B,+BAA+B;AAE/B,8DAAqC;AACrC,oDAAyB;AACzB,oEAAkC;AAElC,MAAM,IAAI,GAAG,IAAA,gBAAS,EAAC,oBAAgB,CAAC,CAAC;AAEzC;;GAEG;AACI,KAAK,UAAU,aAAa,CACjC,WAAmB,EACnB,UAAkB;IAElB,IAAI,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QACjC,MAAM,IAAA,qBAAU,EAAC,WAAW,EAAE,EAAC,GAAG,EAAE,UAAU,EAAC,CAAC,CAAC;IACnD,CAAC;SAAM,IAAI,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;QAC5C,MAAM,UAAU,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;IAC5C,CAAC;SAAM,IAAI,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QACxC,MAAM,IAAA,gBAAK,EAAC,UAAU,CAAC,CAAC;QACxB,MAAM,UAAU,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;IAC5C,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,KAAK,CAAC,+BAA+B,WAAW,EAAE,CAAC,CAAC;IAChE,CAAC;AACH,CAAC;AAdD,sCAcC;AAED;;GAEG;AACH,SAAS,UAAU,CAAC,OAAe,EAAE,UAAkB;IACrD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,MAAM,SAAS,GAAG,gBAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC1C,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC9B,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAChC,MAAM,UAAU,GAAG,IAAA,qBAAgB,EAAC,OAAO,CAAC,CAAC;QAC7C,UAAU,CAAC,IAAI,CAAC,IAAA,wBAAI,GAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,UAAU,CAAC,OAAe,EAAE,UAAkB;IAC3D,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAI,CACzB,yCAAyC,OAAO,GAAG,CACpD,CAAC;IAEF,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;IACjD,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,MAAM,IAAI,KAAK,CAAC,iCAAiC,MAAM,EAAE,CAAC,CAAC;IAC7D,CAAC;IACD,MAAM,SAAS,GAAG,OAAO,CAAC,CAAC,CAAE,CAAC;IAE9B,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,MAAM,IAAA,kBAAO,EAAC,SAAS,CAAC,CAAC;QAC3C,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACpC,OAAO,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,sBAAsB,SAAS,EAAE,CAAC,CAAC;QACrD,CAAC;QACD,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,SAAU,EAAE,OAAO,CAAC,CAAC;QAEnD,MAAM,IAAI,CAAC,UAAU,WAAW,MAAM,UAAU,GAAG,CAAC,CAAC;IACvD,CAAC;YAAS,CAAC;QACT,MAAM,IAAI,CAAC,mBAAmB,SAAS,UAAU,CAAC,CAAC;IACrD,CAAC;AACH,CAAC"}