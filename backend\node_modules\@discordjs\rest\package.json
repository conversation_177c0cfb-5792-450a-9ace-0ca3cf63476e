{"$schema": "https://json.schemastore.org/package.json", "name": "@discordjs/rest", "version": "2.5.0", "description": "The REST API for discord.js", "exports": {".": {"node": {"require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}, "default": {"require": {"types": "./dist/web.d.ts", "default": "./dist/web.js"}, "import": {"types": "./dist/web.d.mts", "default": "./dist/web.mjs"}}}, "./*": {"require": {"types": "./dist/strategies/*.d.ts", "default": "./dist/strategies/*.js"}, "import": {"types": "./dist/strategies/*.d.mts", "default": "./dist/strategies/*.mjs"}}}, "types": "./dist/index.d.ts", "directories": {"lib": "src", "test": "__tests__"}, "files": ["dist"], "contributors": ["Crawl <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "SpaceEEC <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> Rom<PERSON> <<EMAIL>>"], "license": "Apache-2.0", "keywords": ["discord", "api", "rest", "discordapp", "discordjs"], "repository": {"type": "git", "url": "https://github.com/discordjs/discord.js.git", "directory": "packages/rest"}, "bugs": {"url": "https://github.com/discordjs/discord.js/issues"}, "homepage": "https://discord.js.org", "funding": "https://github.com/discordjs/discord.js?sponsor", "dependencies": {"@sapphire/async-queue": "^1.5.3", "@sapphire/snowflake": "^3.5.3", "@vladfrangu/async_event_emitter": "^2.4.6", "discord-api-types": "^0.38.1", "magic-bytes.js": "^1.10.0", "tslib": "^2.6.3", "undici": "6.21.1", "@discordjs/util": "^1.1.1", "@discordjs/collection": "^2.1.1"}, "devDependencies": {"@favware/cliff-jumper": "^4.1.0", "@types/node": "18.17.9", "@vitest/coverage-v8": "^2.0.5", "cross-env": "^7.0.3", "esbuild-plugin-version-injector": "^1.2.1", "eslint": "^8.57.0", "eslint-config-neon": "^0.1.62", "eslint-formatter-pretty": "^6.0.1", "prettier": "^3.3.3", "tsup": "^8.2.4", "turbo": "^2.0.14", "typescript": "~5.5.4", "vitest": "^2.0.5", "@discordjs/api-extractor": "^7.38.1", "@discordjs/scripts": "^0.1.0"}, "engines": {"node": ">=18"}, "publishConfig": {"access": "public", "provenance": true}, "scripts": {"test": "vitest run", "build": "tsc --noEmit && tsup", "build:docs": "tsc -p tsconfig.docs.json", "lint": "prettier --check . && cross-env TIMING=1 eslint --format=pretty src __tests__", "format": "prettier --write . && cross-env TIMING=1 eslint --fix --format=pretty src __tests__", "fmt": "pnpm run format", "docs": "pnpm run build:docs && api-extractor run --local --minify && generate-split-documentation", "changelog": "git cliff --prepend ./CHANGELOG.md -u -c ./cliff.toml -r ../../ --include-path 'packages/rest/*'", "release": "cliff-jumper"}}