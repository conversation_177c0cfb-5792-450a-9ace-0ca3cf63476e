# X.com Monitor

## Setup

1.  Install Node.js and npm.
2.  Install PostgreSQL.
    *   Start PostgreSQL: The method depends on your OS. On Windows, use the Services app. On macOS, use `brew services start postgresql` or find the app. On Linux, use `sudo systemctl start postgresql`.
    *   Find the PostgreSQL URL: The default URL is `postgres://user:password@host:port/database`. Check your PostgreSQL configuration for the exact URL.
3.  Create a Discord bot and obtain its token.
4.  Create a .env file in the backend and discord-bot directories with the following variables:

    *   DISCORD\_BOT\_TOKEN=your\_discord\_bot\_token
    *   DISCORD\_CLIENT\_ID=your\_discord\_client\_id
    *   DATABASE\_URL=your\_database\_url
5.  Install the dependencies for each component:

    *   cd backend && npm install
    *   cd discord-bot && npm install

## Build

1.  Build the backend:

    *   cd backend && npm run build
2.  Build the discord bot:

    *   cd discord-bot && npm run build

## Deployment

1.  Deploy the backend to a Node.js hosting platform (e.g., Heroku, AWS).
2.  Run the discord bot:

    *   cd discord-bot && npm start
