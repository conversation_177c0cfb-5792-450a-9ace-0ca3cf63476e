# Installation
> `npm install --save @types/continuation-local-storage`

# Summary
This package contains type definitions for continuation-local-storage (https://github.com/othiym23/node-continuation-local-storage).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/continuation-local-storage.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/continuation-local-storage/index.d.ts)
````ts
/// <reference types="node" />

export type Context = {
    [key: string]: any;
};

export type BindCallbackFn<T> = (...args: any[]) => T;
export type RunCallbackFn<T> = (context: Context) => T;

export interface Namespace {
    readonly name: string; // Note: this is readonly because changing it does not actually rename it

    readonly active: Context; // Note: this is readonly because changing it manually will break functionality
    createContext(): Context;

    set<T>(key: string, value: T): T;
    get<T>(key: string): T | undefined;

    run<T = void>(callback: RunCallbackFn<T>): Context;
    runAndReturn<T>(callback: RunCallbackFn<T>): T;

    bind<T = void>(callback: BindCallbackFn<T>, context?: Context): BindCallbackFn<T>;
    bindEmitter(emitter: NodeJS.EventEmitter): void;

    enter(context: Context): void;
    exit(context: Context): void;
}

export function createNamespace(name: string): Namespace;
export function getNamespace(name: string): Namespace | undefined;
export function destroyNamespace(name: string): void;
export function reset(): void;

// declare namespace process {
//     var namespaces: ContinuationLocalStorage.Namespace[];
// }

````

### Additional Details
 * Last updated: Mon, 06 Nov 2023 22:41:05 GMT
 * Dependencies: [@types/node](https://npmjs.com/package/@types/node)

# Credits
These definitions were written by [Jang-Ho Hwang](https://github.com/rath), [Kei Son](https://github.com/heycalmdown), and [Dmitry Kudryavtsev](https://github.com/skwee357).
